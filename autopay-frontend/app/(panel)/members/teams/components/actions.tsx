import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { FaWrench, FaTrash, FaPlus, FaUsers } from 'react-icons/fa';
import React from 'react';
import NiceModal from '@ebay/nice-modal-react';
import Link from 'next/link';
import { RiVipCrown2Line } from 'react-icons/ri';
import { FaServer } from 'react-icons/fa6';

export function Actions({
  triggerChildren,
  team,
}: {
  triggerChildren: React.ReactNode;
  team: Team;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{triggerChildren}</DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => NiceModal.show('addTeamMemberIndex', { team })}
        >
          <FaPlus className="mr-2 h-4 w-4" />
          Add Member
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}/servers`}>
            <FaServer className="mr-2 h-4 w-4" />
            Servers
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}/members`}>
            <FaUsers className="mr-2 h-4 w-4" />
            Members
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}/roles`}>
            <RiVipCrown2Line className="mr-2 h-4 w-4" />
            Roles
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}`}>
            <FaWrench className="mr-2 h-4 w-4" />
            Settings
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="focus:bg-destructive focus:text-white"
          onClick={() => NiceModal.show('deleteTeam', { team })}
        >
          <FaTrash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
