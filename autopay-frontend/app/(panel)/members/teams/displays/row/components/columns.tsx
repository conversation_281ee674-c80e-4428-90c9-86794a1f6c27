import { ColumnDef } from '@tanstack/react-table'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Wrench } from 'lucide-react'
import Link from 'next/link'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<Team>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Name"
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="group flex space-x-2">
          <span className="font-medium">{row.getValue('name')}</span>

          <Button
            size="icon"
            variant="outline"
            className="h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100"
            data-tooltip-html="Settings"
            asChild>
            <Link href={`/teams/${row.original.id}`}>
              <Wrench className="h-3 w-3" />
            </Link>
          </Button>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'description',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Description"
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="group flex space-x-2">
          <span className="font-medium">{row.getValue('description')}</span>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'avatar',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Avatar"
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-center">
          <Avatar className="h-6 w-6">
            <AvatarImage
              src={row.getValue('avatar')}
              alt={row.getValue('name')}
              className="rounded-full border"
            />
            <AvatarFallback>{row.getValue('name')}</AvatarFallback>
          </Avatar>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'users_count',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Members"
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      return <div className="flex items-center justify-center">{row.getValue('users_count')}</div>
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <div className="flex items-center justify-end">
        <DataTableRowActions row={row} />
      </div>
    ),
  },
]
