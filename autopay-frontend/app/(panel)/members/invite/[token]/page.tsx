'use client'

import Congratulations from '@/assets/vectors/developers/ARSK-_notion-developer-daily-discuss-with-team.svg'
import Loading from '@/components/display/Loading'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import { use } from 'react'

type Props = Promise<{
  token: string
}>

export default function Component({ params }: { params: Props }) {
  const { token } = use(params)

  const { isPending, isError } = useQuery({
    queryKey: ['teamAccept', token],
    queryFn: (): Promise<ApiResponsePagination> =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/accept/` + token),
  })

  const renderBodyContent = () => {
    if (isPending) {
      return <Loading loadingText="Please wait..." />
    }

    if (isError) {
      return (
        <div className="flex items-center">
          <Alert variant="destructive">
            <AlertDescription>We encountered an error while processing your request.</AlertDescription>
          </Alert>
        </div>
      )
    }

    return (
      <div>
        <span className="text-lg font-bold">Congratulations!</span>
        <br />
        You have successfully joined your new team.
      </div>
    )
  }

  return (
    <>
      <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
        <Image
          src={Congratulations}
          alt="Teams"
          width={300}
          priority={true}
        />
        {renderBodyContent()}
      </div>
    </>
  )
}
