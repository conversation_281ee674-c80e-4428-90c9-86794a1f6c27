import { z } from 'zod'

// Schema for organization member data
export const memberSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  is_owner: z.boolean().nullable().optional(),
  is_active: z.boolean().nullable().optional(),
  status: z.string().nullable().optional(),
  joined_at: z.string().nullable().optional(),
  roles_count: z.number().nullable().optional(),
  teams_count: z.number().nullable().optional(),
})

export type Member = z.infer<typeof memberSchema>
