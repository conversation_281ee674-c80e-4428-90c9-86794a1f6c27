import { ColumnDef } from '@tanstack/react-table'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatDistanceToNow } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Crown, Settings } from 'lucide-react'
import Link from 'next/link'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'
import { Member } from '../data/schema'

export const columns: ColumnDef<Member>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Thành viên"
      />
    ),
    cell: ({ row }) => {
      const member = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={`https://api.dicebear.com/7.x/initials/svg?seed=${member.name}`}
              alt={member.name}
            />
            <AvatarFallback>
              {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{member.name}</span>
              {member.is_owner && (
                <Crown className="h-4 w-4 text-yellow-500" />
              )}
            </div>
            <span className="text-sm text-muted-foreground">{member.email}</span>
          </div>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'teams_count',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Teams"
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      const count = row.getValue('teams_count') as number
      return (
        <div className="flex items-center justify-center">
          <Badge variant="secondary">
            {count || 0} team{count !== 1 ? 's' : ''}
          </Badge>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'roles_count',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Roles"
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      const count = row.getValue('roles_count') as number
      return (
        <div className="flex items-center justify-center">
          <Badge variant="outline">
            {count || 0} role{count !== 1 ? 's' : ''}
          </Badge>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Trạng thái"
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      const isActive = status === 'active'
      return (
        <div className="flex items-center justify-center">
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Hoạt động' : 'Không hoạt động'}
          </Badge>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'joined_at',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Tham gia"
      />
    ),
    cell: ({ row }) => {
      const joinedAt = row.getValue('joined_at') as string
      if (!joinedAt) return <span className="text-muted-foreground">-</span>
      
      try {
        const date = new Date(joinedAt)
        return (
          <span className="text-sm">
            {formatDistanceToNow(date, { addSuffix: true, locale: vi })}
          </span>
        )
      } catch {
        return <span className="text-muted-foreground">-</span>
      }
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <div className="flex items-center justify-end">
        <DataTableRowActions row={row} />
      </div>
    ),
  },
]
