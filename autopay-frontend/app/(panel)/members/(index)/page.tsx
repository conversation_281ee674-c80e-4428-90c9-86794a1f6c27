'use client'

import { useStore } from '@/app/(panel)/members/(index)/stores/store'
import EmptyState from '@/assets/vectors/empty-states/09.svg'
import EmptyStateNoRecord from '@/assets/vectors/empty-states/13.svg'
import ButtonCustom from '@/components/custom-ui/button-custom'
import Loading from '@/components/display/Loading'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { useRef, useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { FiPlusCircle } from 'react-icons/fi'
import { useDebounceCallback } from 'usehooks-ts'

import { buildQueryString } from '@/lib/utils'

const RowLayout = dynamic(() => import('@/app/(panel)/members/(index)/displays/row'), {
  ssr: true,
  loading: () => <Loading />,
})

export default function MembersPage() {
  const { refetchFlag, refetchTrigger, pagination } = useStore()
  const { user } = useUser()

  const searchRef = useRef<HTMLInputElement>(null)
  const [search, setSearch] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)
  const clearSearch = () => {
    if (searchRef.current) {
      searchRef.current.value = ''
    }
    setSearch('')
  }

  const { isPending, error, data } = useQuery({
    queryKey: ['getOrganizationMembers', refetchFlag, search, pagination, user?.current_organization?.alias],
    queryFn: async (): Promise<ApiResponseWithDataPaginationField> => {
      if (!user?.current_organization?.alias) {
        throw new Error('No organization context available')
      }

      const queryString = buildQueryString({
        filter: {
          search: search,
        },
        page: pagination.pageIndex + 1 + '',
        per_page: pagination.pageSize + '',
      })

      return await queryFetchHelper(`/${user.current_organization.alias}/members?${queryString}`)
    },
    enabled: !!user?.current_organization?.alias,
  })

  const renderBodyContent = () => {
    if (isPending) {
      return <Loading />
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription className="text-xs">
            Không thể tải danh sách thành viên. Vui lòng thử lại sau.
          </AlertDescription>
        </Alert>
      )
    }

    if (!data?.data?.data?.length && !search) {
      return (
        <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
          <Image
            src={EmptyState}
            alt="Members"
            width={300}
            priority={true}
          />
          <div>
            Chưa có thành viên nào.
            <br />
            Mời thành viên đầu tiên vào tổ chức
          </div>
          <Button
            variant="outline"
            size="lg"
            onClick={() => {}}>
            <FiPlusCircle className="mr-2 h-4 w-4" />
            Mời thành viên
          </Button>
        </div>
      )
    }

    if (!data?.data?.data?.length) {
      return (
        <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
          <Image
            src={EmptyStateNoRecord}
            alt="No Members"
            width={300}
            priority={true}
          />
          <div className="flex max-w-sm flex-col gap-2">
            <strong>Không tìm thấy thành viên.</strong>
            <p>
              Tìm kiếm &quot;{search}&quot; không khớp với thành viên nào.
              <br />
              Vui lòng thử lại.
            </p>
          </div>
          <div className="flex gap-4">
            <Button
              variant="outline"
              size="lg"
              onClick={() => clearSearch()}>
              Xóa tìm kiếm
            </Button>
            <Button
              size="lg"
              onClick={() => {}}>
              <FiPlusCircle className="mr-2 h-4 w-4" />
              Mời thành viên
            </Button>
          </div>
        </div>
      )
    }

    return <RowLayout apiData={data} />
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h3 className="text-lg font-semibold">Quản lý thành viên</h3>
          <p className="text-muted-foreground text-sm">Quản lý thành viên trong tất cả các teams.</p>
        </div>
        <div className="flex items-center justify-between space-x-2">
          <Input
            ref={searchRef}
            type="search"
            placeholder="Tìm kiếm thành viên..."
            className="w-[200px]"
            defaultValue={search}
            onChange={(event) => debounced(event.target.value)}
          />
          <ButtonCustom
            md="icon"
            lg="default"
            onClick={() => {}}>
            <FiPlusCircle className="h-4 w-4 md:mr-2" />
            <span className="hidden md:block">Mời thành viên</span>
          </ButtonCustom>
        </div>
      </div>
      {renderBodyContent()}
    </div>
  )
}
